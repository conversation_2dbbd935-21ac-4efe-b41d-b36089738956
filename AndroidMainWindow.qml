pragma ComponentBehavior: Bound
import QtQuick
import QtQuick.Layouts
import QtQml
import QtQuick.Controls

Rectangle {
    id: mainWindow

    width: parent.width
    height: parent.height

    signal showDevices

    property string devName: "..."
    property string frimVer: "..."
    property string mac: "..."
    property string status: "..."
    property string wifi_status: "..."
    property string wifi_signal: "..."
    property string smart_home_Status: "..."
    property int ota_progress: 0



    Connections {
        target: Device

        function onLogToApp(message, color)
        {
//            logsModel.append({"log_message": message, "log_entry_color": color})
        }

        function onServiceScanCompleted()
        {
            Device.connectToService("0xf3")
        }

        function onGuid_data_changed(data_type, data)
        {
            switch(data_type) {
                case "device_name":
                    mainWindow.devName = data
                    break
                case "connection_status":
                    if(data === "connected") {
                        mainWindow.status = "connected"
                    } else {
                        mainWindow.status = "disconnected"
                        mainWindow.wifi_status = "disconnected"
                        mainWindow.wifi_signal = ".."
                        mainWindow.smart_home_Status = "disconnected"
                    }
                    break
                case "firm_ver":
                    mainWindow.frimVer = data
                    break
                case "wifi_status":
                    mainWindow.wifi_status = data
                    break
                case "wifi_level":
                    mainWindow.wifi_signal = data
                    break
                case "bsh_connection":
                    mainWindow.smart_home_Status = data
                    break
                case "ota_progress":
                    mainWindow.ota_progress = +data
                    break
            }
        }




    }




    // Top row with device items
    Rectangle {
        id: top_row
        width: parent.width
        height: 150
        color: "#96d0b1"

        ColumnLayout {
//                id: layout
            anchors.fill: parent
            spacing: 1

            MainWindowHeaderItem {
                text_size: 18
                id: devName
                labelName: "Device name:"
                labelValue: mainWindow.devName
                Layout.fillWidth: true
                Layout.leftMargin: 3
                Layout.topMargin: 5
            }

            MainWindowHeaderItem {
                id: firmVer
                text_size: 18
                labelName: "Firmware ver:"
                labelValue: mainWindow.frimVer
                Layout.fillWidth: true
                Layout.leftMargin: 3
            }

            MainWindowHeaderItem {
                id: wifi_text
                text_size: 18
                labelName: "WIFI: "
                labelValue: mainWindow.wifi_status
                Layout.fillWidth: true
                Layout.leftMargin: 3
            }

            MainWindowHeaderItem {
                id: wifi_signal_text
                text_size: 22
                labelName: "WIFI signal: "
                labelValue: mainWindow.wifi_signal
                Layout.fillWidth: true
                Layout.leftMargin: 3
            }
        }
    }



    // controls buttons

    Rectangle {
        signal showDevices

        id: controls

        anchors.left: parent.left
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.top: top_row.bottom

        color: "lightgrey"


        BleCmdButton {
            id: setWifiSbtn
            anchors.left: parent.left
            anchors.top: parent.top
            btnText: "Set Wifi ssid"
            btnValueHint: "enter ssid"
            text_size: 18
            onButtonClick: {
                Device.sendToBle("wifi_ssid", setWifiSbtn.btnData)
            }
        }

        BleCmdButton {
            id: setWifiPbtn
            anchors.left: parent.left
            anchors.top: setWifiSbtn.bottom
            text_size: 18
            btnText: "Set Wifi pswd"
            btnValueHint: "enter password"
            onButtonClick: {
                Device.sendToBle("wifi_pswd", setWifiPbtn.btnData)
            }
        }


        /***************************************s
        *     connect commands
        ****************************************/
        RowLayout {
            id: connectButtonsRect
            width: parent.width
//            height: 50
            anchors.top: setWifiPbtn.bottom
            anchors.topMargin: 10
            anchors.bottomMargin: 10
            spacing: 5

            JustButton {
                id: conWifiBtn
                btnText: "connect to WIFI"
                height: 50
                text_size: 18
                onButtonClick: {
                    Device.sendToBle("conn_wifi", "")
                }
            }

        }

        // send to uart button
        BleCmdButton {
            id: sendToUartBtn
            anchors.left: parent.left
            anchors.top: connectButtonsRect.bottom
            anchors.topMargin: 5
            btnText: "Send to UART"
            btnValueHint: "55 AA"
            onButtonClick: {
                Device.sendToBle("uart_pkt", sendToUartBtn.btnData)
            }
        }

        BleCmdButton {
            id: frwm_with_link
            anchors.left: parent.left
            anchors.top: sendToUartBtn.bottom
            anchors.topMargin: 5
            btnText: "Firmware update"
            btnValueHint: "http://..."
            onButtonClick: {
                Device.sendToBle("ota_with_link", frwm_with_link.btnData)
            }
        }

        // test and other buttons
        RowLayout {
            id: testButtonsRow
            width: parent.width
//            height: 50
            anchors.top: frwm_with_link.bottom
            anchors.topMargin: 5
            spacing: 5

            JustButton {
                id: startOtaDebBtn
                btnText: "Firm upd hardcoded link"
                height: 50
                text_size: 18
                onButtonClick: {
                    Device.sendToBle("ota", "")
                }
            }
        }


        ProgressBar {
            id: ota_progress_bar
            width: parent.width
            height: 30
            anchors.top: testButtonsRow.bottom

            from: 0
            to: 100
            value: ota_progress
            visible: ota_progress > 0 ? true : false
            background: Rectangle {
                                            anchors.fill: ota_progress_bar
                                            anchors.leftMargin: 5
                                            anchors.rightMargin: 5
                                            color: "lightslategray"
//                                            radius: 4
//                                            border.width: 1
//                                            border.color: "black"
                                    }
            contentItem: Rectangle {
                                            anchors.left: ota_progress_bar.lefts
                                            anchors.bottom: ota_progress_bar.bottom

                                            anchors.leftMargin: 5
                                            anchors.rightMargin: 5

                                            height: ota_progress_bar.height
                                            width: ota_progress_bar.width * ota_progress_bar.value/100

                                            color: "lightgreen"
                                            radius: 4
                                        }
        }

        // bottom buttons

        BottomButton {
            id: backBtn
            anchors.bottom: parent.bottom
//            anchors.left: connectBtn.right
            anchors.leftMargin: 5
            anchors.rightMargin: 5
            width: parent.width
            height: 90
            text_size: 18
            btnText: "Back to scanner"

            onButtonClick: {
                Device.disconnectFromDevice()
                showDevices()
                Device.update = "Search"
            }
        }

    }
}

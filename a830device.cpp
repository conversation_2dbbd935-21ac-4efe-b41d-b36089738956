#include "a830device.h"
#include <QtCore/qdebug.h>

A830Device::A830Device() : BleDevice()
{
    // A830-specific initialization
}

A830Device::~A830Device()
{
    // A830-specific cleanup
}

void A830Device::handleCharacteristicChanged(const QLowEnergyCharacteristic &c, const QByteArray &value)
{
    if (c.uuid().toString() == "{0000f304-0000-1000-8000-00805f9b34fb}")
    {
        qWarning() << "======= RX incoming data ========";
        charReadCb(value);
    }
    else if (c.uuid().toString() == "{0000f305-0000-1000-8000-00805f9b34fb}")
    {
        QByteArray tmp = c.value();
        QString wifi_connection = tmp[0] == 0 ? "disconnected" : "connected";
        QString wifi_level = QString::number(tmp[1]);
        QString bsh_connection = tmp[2] == 0 ? "disconnected" : "connected";
        int ota_progress = tmp[3];

        if(ota_progress != 0) log(SYS_LOG_PREFIX + "OTA progress: " + QString::number(ota_progress), SYS_LOG_COLOR);

        updateData("wifi_status", wifi_connection);
        updateData("wifi_level", wifi_level);
        updateData("bsh_connection", bsh_connection);
        updateData("ota_progress", QString::number(ota_progress));
    }
    else if (c.uuid().toString() == "{0000f306-0000-1000-8000-00805f9b34fb}")
    {
        qWarning() << "======= Telemetry incoming data ========";
        telemReadCb(value);
    }
}

void A830Device::handleCharacteristicRead(const QLowEnergyCharacteristic &info, const QByteArray &value)
{
    qWarning() << "======= incoming ble data. read cb ========";

    // if its firmware version
    if (info.uuid().toString() == "{0000f302-0000-1000-8000-00805f9b34fb}")
    {
        updateData("firm_ver", QString(info.value()));
        return;
    }

    // if its device subtypetype (different hardware versions etc)
    if (info.uuid().toString() == "{0000f301-0000-1000-8000-00805f9b34fb}")
    {
        QString tmp;
        // Note: We don't have access to currentDevice.getName() here
        // This will need to be handled differently or passed in
        tmp.append("A830");  // Placeholder
        tmp.append("    type: ");
        tmp.append(byteArrayToString(info.value()));
        updateData("device_name", tmp);
        return;
    }

    charReadCb(value);
}

void A830Device::telemReadCb(const QByteArray &value)
{
    parse_telemetry(value);
}

void A830Device::parse_telemetry(const QByteArray &value)
{
    if(value.size() < 3) return;

    int shift_pos = 2; // shift to actor currently being parsed
    QString result = "";
    for (int i = 0; i < value[1]; ++i) {
        result.append(QString(value.mid(shift_pos, 3)));
        int val = uint8_t(value[shift_pos + 7]) + (uint8_t)(value[shift_pos + 8])*0x100 +
                 (uint8_t)(value[shift_pos + 9])*0x10000 + (uint8_t)(value[shift_pos + 10])*0x1000000;

        result.append(QString::number(val));
        result.append("|");
        shift_pos += 11;
    }
    updateData("telemetry", result);
}

void A830Device::charReadCb(const QByteArray &value)
{
    // prettier log by spacing data from packet service bytes
    if (value[0] == (char)0xAA && value[1] != (char)0x00 && value.size() > 3)
    {
        if (value[1] == (char)0xB2 || value[1] == (char)0xB0 || value[1] == (char)0xB1)
        {
            QString tmp = byteArrayToString(value);
            tmp.insert(6, "     ");
            log(BLE_IN_LOG_PREFIX + tmp, BLE_IN_LOG_COLOR);
        } else {
            QString tmp = byteArrayToString(value);
            tmp.insert(10, "     ");
            log(BLE_IN_LOG_PREFIX + tmp, BLE_IN_LOG_COLOR);
        }
    } else {
        log(BLE_IN_LOG_PREFIX + byteArrayToString(value), BLE_IN_LOG_COLOR);
    }

    QByteArray arr;

    // sending wifi ssid
    if (value[0] == (char)0xAA && (value[1] == (char)CMD_SET_WIFI_SSID || value[1] == (char)(CMD_SET_WIFI_SSID + 1))) {
        if (value[1] == (char)(CMD_SET_WIFI_SSID + 1) && next_ssid_piece_to_send == 0) {
            // last piece already sent
            arr.append(0x12);
        } else {
            arr.append(CMD_SET_WIFI_SSID +1);       // command
            arr.append(next_ssid_piece_to_send);    // piece N
            QByteArray tmp = ssid.toUtf8();
            tmp.remove(0, 20*(next_ssid_piece_to_send - 1));
            arr.append(tmp.size() >= 20 ? 20 : tmp.size()); // pice size
            arr.append(tmp, tmp.size() >= 20 ? 20 : tmp.size());  // data
            next_ssid_piece_to_send++;
            if (tmp.size() <= 20) next_ssid_piece_to_send = 0;
        }

        m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
    }

    // sending wifi password
    if (value[0] == (char)0xAA && (value[1] == (char)CMD_SET_WIFI_PSWD || value[1] == (char)(CMD_SET_WIFI_PSWD + 1))) {
        if (value[1] == (char)(CMD_SET_WIFI_PSWD + 1) && next_pswd_piece_to_send == 0) {
            // last piece already sent
            arr.append(CMD_SET_WIFI_PSWD + 2);
        } else {
            arr.append(CMD_SET_WIFI_PSWD +1);       // command
            arr.append(next_pswd_piece_to_send);    // piece N
            QByteArray tmp = wifi_pswd.toUtf8();
            tmp.remove(0, 20*(next_pswd_piece_to_send - 1));
            arr.append(tmp.size() >= 20 ? 20 : tmp.size()); // pice size
            arr.append(tmp, tmp.size() >= 20 ? 20 : tmp.size());  // data
            next_pswd_piece_to_send++;
            if (tmp.size() <= 20) next_pswd_piece_to_send = 0;
        }

        m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
    }

    // sending balancer link
    if (value[0] == (char)0xAA && (value[1] == (char)CMD_SET_BLCR_LINK || value[1] == (char)(CMD_SET_BLCR_LINK + 1))) {
        if (value[1] == (char)(CMD_SET_BLCR_LINK + 1) && next_blcr_piece_to_send == 0) {
            // last piece already sent
            arr.append(CMD_SET_BLCR_LINK + 2);
        } else {
            arr.append(CMD_SET_BLCR_LINK +1);       // command
            arr.append(next_blcr_piece_to_send);    // piece N
            QByteArray tmp = blcr_link.toUtf8();
            tmp.remove(0, 20*(next_blcr_piece_to_send - 1));
            arr.append(tmp.size() >= 20 ? 20 : tmp.size()); // pice size
            arr.append(tmp, tmp.size() >= 20 ? 20 : tmp.size());  // data
            next_blcr_piece_to_send++;
            if (tmp.size() <= 20) next_blcr_piece_to_send = 0;
        }

        m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
    }

    // sending mqtt user id
    if (value[0] == (char)0xAA && (value[1] == (char)CMD_MQTT_UID || value[1] == (char)(CMD_MQTT_UID + 1))) {
        if (value[1] == (char)(CMD_MQTT_UID + 1) && next_uid_piece_to_send == 0) {
            // last piece already sent
            arr.append(CMD_MQTT_UID + 2);
        } else {
            arr.append(CMD_MQTT_UID +1);       // command
            arr.append(next_uid_piece_to_send);    // piece N
            QByteArray tmp = mqtt_uid.toUtf8();
            tmp.remove(0, 20*(next_uid_piece_to_send - 1));
            arr.append(tmp.size() >= 20 ? 20 : tmp.size()); // pice size
            arr.append(tmp, tmp.size() >= 20 ? 20 : tmp.size());  // data
            next_blcr_piece_to_send++;
            if (tmp.size() <= 20) next_uid_piece_to_send = 0;
        }

        m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
    }

    // sending mqtt password
    if (value[0] == (char)0xAA && (value[1] == (char)CMD_MQTT_PSWD || value[1] == (char)(CMD_MQTT_PSWD + 1))) {
        if (value[1] == (char)(CMD_MQTT_PSWD + 1) && next_mq_pswd_piece_to_send == 0) {
            // last piece already sent
            arr.append(CMD_MQTT_PSWD + 2);
        } else {
            arr.append(CMD_MQTT_PSWD +1);       // command
            arr.append(next_mq_pswd_piece_to_send);    // piece N
            QByteArray tmp = mqtt_pswd.toUtf8();
            tmp.remove(0, 20*(next_mq_pswd_piece_to_send - 1));
            arr.append(tmp.size() >= 20 ? 20 : tmp.size()); // pice size
            arr.append(tmp, tmp.size() >= 20 ? 20 : tmp.size());  // data
            next_blcr_piece_to_send++;
            if (tmp.size() <= 20) next_mq_pswd_piece_to_send = 0;
        }

        m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
    }

    // if receiving challenge sign
    static int ch_sign_size;
    static int ch_sign_pieces;
    static int ch_piece_to_request;
    if (value[0] == (char)0xAA && value[1] == (char)CMD_SEND_CHALLENGE_SIGN_SIZE)
    {
        ch_sign_size = value[2] + value[3]*0xff;
        arr.append(CMD_SEND_CHALLENGE_SIGN_PIECE_SIZE);
        m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
        log(QString(BLE_OUT_LOG_PREFIX) + " send ch. piece size    "+ byteArrayToString(arr), BLE_OUT_LOG_COLOR);
    }
    if(value[0] == (char)0xAA && value[1] == (char)CMD_SEND_CHALLENGE_SIGN_PIECE_SIZE)
    {
        ch_sign_pieces = ch_sign_size/value[2] + 1;
        qWarning() << "challenge sign pieces qty: " << ch_sign_pieces;
        ch_piece_to_request = 0;

        arr.append(CMD_SEND_CHALLENGE_SIGN_PIECE);
        arr.append((char)ch_piece_to_request);
        m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
        log(QString(BLE_OUT_LOG_PREFIX) + " send ch. piece   " + byteArrayToString(arr), BLE_OUT_LOG_COLOR);
    }
    if(value[0] == (char)0xAA && value[1] == (char)CMD_SEND_CHALLENGE_SIGN_PIECE)
    {
        ch_piece_to_request++;
        if (ch_piece_to_request >= ch_sign_pieces-1)
        {
            // TODO store pieces and log them at this moment
        } else {
            arr.append(CMD_SEND_CHALLENGE_SIGN_PIECE);
            arr.append((char)ch_piece_to_request);
            m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
            log(QString(BLE_OUT_LOG_PREFIX) + " send ch. piece     " + byteArrayToString(arr), BLE_OUT_LOG_COLOR);
        }
    }

    if(value[0] == (char)0xAA && value[1] == (char)CMD_CONN_SH)
    {
        if(value[3] == (char)0xCA) updateData("wifi_status", "connected");
    }
    if(value[0] == (char)0x6f && value[1] == (char)0x74 && value[2] == (char)0x61)
    {
        if(value[7] != 0)
        {
            int arg = value[7];
            QString ota_progress = QString("%1").arg(arg, 0, 10);
            updateData("ota_progress", ota_progress);
        }
    }
}

// Copyright (C) 2013 BlackBerry Limited. All rights reserved.
// Copyright (C) 2023 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR BSD-3-Clause

pragma ComponentBehavior: Bound
//import QtQuick.Controls
//import QtQuick
//import QtCore
import QtQuick
//import QtQuick.Layouts
import QtQuick.Controls as My

Rectangle {
    id: devicesPage

    property bool deviceState: Device.state
    signal showServices(device_name: string)

//    width: 200
//    height: 800

    onDeviceStateChanged: {
        if (!Device.state)
            info.visible = false
    }

    Header {
        id: header
        anchors.top: parent.top
        headerText: {
            if (Device.state)
                return "Discovering"

            if (Device.devicesList.length > 0)
                return "Select a device"

            return "Start Discovery"
        }
    }

    Dialog {
        id: info
        anchors.centerIn: parent
        visible: false
        width: 140
    }

    ListView {
        id: theListView
        width: parent.width
        clip: true

        anchors.top: header.bottom
        anchors.bottom: connectToggle.top
        model: Device.filteredDevicesList

        delegate: Rectangle {
            required property var modelData
            id: box
            height: 60
            width: theListView.width
            color: "#a2dafc"
            border.width: 1
            border.color: "black"
            radius: 0

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    Device.stopDeviceDiscovery()
                    Device.scanServices(box.modelData.deviceAddress)
                    showServices(box.modelData.deviceName)
                }
            }

            Label {
                id: deviceName
//                textContent: box.modelData === null ? "-" : box.modelData.deviceName
                textContent: box.modelData.deviceName
                anchors.top: parent.top
                anchors.topMargin: 5
            }

            Label {
                id: deviceAddress
//                textContent: box.modelData === null ? "-" : box.modelData.deviceAddress
                textContent: box.modelData.deviceAddress
                font.pointSize: deviceName.font.pointSize * 0.7
                anchors.bottom: box.bottom
                anchors.bottomMargin: 5
            }
        }
    }

    Menu {
        id: connectToggle

        menuWidth: parent.width
        anchors.bottom: pickbox.top
        menuText: {
            visible = Device.devicesList.length > 0
            if (Device.useRandomAddress)
                return "Address type: Random"
            else
                return "Address type: Public"
        }

        onButtonClick: Device.useRandomAddress = !Device.useRandomAddress
    }

    My.ComboBox {
        id: pickbox
        width: parent.width
        height: 60
        anchors.bottom: menu.top
        font.pointSize: 16
        model: ["   All", "   A830", "   A630", "   O80x"]
        onActivated: Device.setDevFilter(pickbox.currentIndex);
    }

    Menu {
        id: menu
        anchors.bottom: parent.bottom
        menuWidth: parent.width
        menuHeight: 60
        menuText: Device.update
        onButtonClick: {
            if (!Device.state) {
                Device.startDeviceDiscovery()
                // if startDeviceDiscovery() failed Device.state is not set
                if (Device.state) {
                    info.dialogText = "Searching..."
                    info.visible = true
                }
            } else {
                Device.stopDeviceDiscovery()
            }
        }
    }
}

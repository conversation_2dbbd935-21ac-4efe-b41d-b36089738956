import QtQuick

Rectangle {
    id: bottomBtn

    property string btnText: "name"
    property int text_size: 14
    signal buttonClick

    height: 40
    width: parent.width / 3 - 7

    color: "#96d0b1"
    border.width: 1
    border.color: "#E3E3E3"
    radius: 5

    anchors.bottomMargin: 5

    Text {
        id: buttonText
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        anchors.fill: parent
        text: bottomBtn.btnText
        elide: Text.ElideMiddle
        color: "black"
        font.pointSize: text_size
    }

    MouseArea {
        anchors.fill: parent
        onPressed: {
            bottomBtn.width = bottomBtn.width - 7
            bottomBtn.height = bottomBtn.height - 5
        }

        onReleased: {
            bottomBtn.width = bottomBtn.width + 7
            bottomBtn.height = bottomBtn.height + 5
        }

        onClicked: {
            bottomBtn.buttonClick()
        }
    }
}

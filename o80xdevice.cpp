#include "o80xdevice.h"
#include <QtCore/qdebug.h>

O80xDevice::O80xDevice() : BleDevice()
{
    // O80x-specific initialization
}

O80xDevice::~O80xDevice()
{
    // O80x-specific cleanup
}

void O80xDevice::sendHeaterCommandToDevice(QString command, QString data, bool dont_send)
{
    QString cmd = command.mid(0, 3);
    QByteArray arr_to_send = {};

    if(cmd == "   ")
    {
        qDebug() << "no command picked";
        return;
    }

    // Special case for OTA command
    if(cmd == "OTA")
    {
        QByteArray arr;
        arr.append(CMD_OTA_HARDCODED_LINK);

        qDebug() << "OTA command composed:" << arr.toHex(' ');
        if (dont_send)
        {
            updateData("command_bytes", arr.toHex(' '));
        }
        else
        {
            if (!checkPointersNotNull())
            {
                log(" Device not connected", BLE_OUT_LOG_COLOR);
                return;
            }
            log(QString(BLE_OUT_LOG_PREFIX) + "ota" + "    " + byteArrayToString(arr), BLE_OUT_LOG_COLOR);
            m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
        }
        return;
    }

    arr_to_send.append(CMD_FAKE_FROM_BROKER);
    if(cmd == "BCT") // read packet
    {
        arr_to_send.append(0x08);
    } else {
        arr_to_send.append(0x0E);
    }

    arr_to_send.append(1);
    arr_to_send.append(1);

    arr_to_send.append(cmd.toUtf8());
    int zero = 0;
    if(cmd == "BCT") // read packet
    {
        arr_to_send.append(zero);
        arr_to_send.append(zero);
        arr_to_send.append(zero);
    } else {
        arr_to_send.append(zero);
        arr_to_send.append(zero);
        arr_to_send.append(1);
        arr_to_send.append(zero);
        arr_to_send.append(zero);
        // append value
        arr_to_send.append(data.toInt() % 0x100);
        arr_to_send.append(data.toInt() / 0x100 % 0x10000);
        arr_to_send.append(data.toInt() / 0x10000 % 0x1000000);
        arr_to_send.append(data.toInt() / 0x1000000 % 0x100000000);
    }

    qDebug() << "command composed:" << arr_to_send.toHex(' ');
    if (dont_send)
    {
        updateData("command_bytes", arr_to_send.toHex(' '));
    } else {
        m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr_to_send, QLowEnergyService::WriteWithResponse);
    }
}

void O80xDevice::handleCharacteristicChanged(const QLowEnergyCharacteristic &c, const QByteArray &value)
{
    if (c.uuid().toString() == "{0000f304-0000-1000-8000-00805f9b34fb}")
    {
        qWarning() << "======= RX incoming data ========";
        log(BLE_IN_LOG_PREFIX + byteArrayToString(value), BLE_IN_LOG_COLOR);
    }
    else if (c.uuid().toString() == "{0000f305-0000-1000-8000-00805f9b34fb}")
    {
        QByteArray tmp = c.value();
        QString wifi_connection = tmp[0] == 0 ? "disconnected" : "connected";
        QString wifi_level = QString::number(tmp[1]);
        QString bsh_connection = tmp[2] == 0 ? "disconnected" : "connected";
        int ota_progress = tmp[3];

        if(ota_progress != 0) log(SYS_LOG_PREFIX + "OTA progress: " + QString::number(ota_progress), SYS_LOG_COLOR);

        updateData("wifi_status", wifi_connection);
        updateData("wifi_level", wifi_level);
        updateData("bsh_connection", bsh_connection);
        updateData("ota_progress", QString::number(ota_progress));
    }
    else if (c.uuid().toString() == "{0000f306-0000-1000-8000-00805f9b34fb}")
    {
        qWarning() << "======= Telemetry incoming data ========";
        qWarning() << byteArrayToString(value);
        log(BLE_IN_LOG_PREFIX + byteArrayToString(value), BLE_IN_LOG_COLOR);
        // O80x-specific telemetry handling
        treatO80xTelemetry(value);
    }
}

void O80xDevice::handleCharacteristicRead(const QLowEnergyCharacteristic &info, const QByteArray &value)
{
    qWarning() << "======= incoming ble data. read cb ========";

    // if its firmware version
    if (info.uuid().toString() == "{0000f302-0000-1000-8000-00805f9b34fb}")
    {
        updateData("firm_ver", QString(info.value()));
        return;
    }

    // if its device subtypetype (different hardware versions etc)
    if (info.uuid().toString() == "{0000f301-0000-1000-8000-00805f9b34fb}")
    {
        QString tmp;
        // Note: We don't have access to currentDevice.getName() here
        // This will need to be handled differently or passed in
        tmp.append("O80x");  // Placeholder
        tmp.append("    type: ");
        tmp.append(byteArrayToString(info.value()));
        updateData("device_name", tmp);
        return;
    }

    log(BLE_IN_LOG_PREFIX + byteArrayToString(value), BLE_IN_LOG_COLOR);
}



void O80xDevice::treatO80xTelemetry(const QByteArray &value)
{
    if(value.size()<3) return;

    int shift_pos = 2; // shift to actor currently being parsed
    QString result = "";
    for (int i = 0; i < value[1]; ++i) {
        if(value.size() - shift_pos < 11) 
        {
            qWarning() << "Wrong telemetry size:" << value.size();
            return;
        }
        result.append(QString(value.mid(shift_pos, 3)));
        int val = uint8_t(value[shift_pos + 7]) + (uint8_t)(value[shift_pos + 8])*0x100 + (uint8_t)(value[shift_pos + 9])*0x10000 + (uint8_t)(value[shift_pos + 10])*0x1000000;

        result.append (QString::number(val));
        result.append ("|");
        shift_pos+= 11;
    }
    updateData("telemetry",result);
}

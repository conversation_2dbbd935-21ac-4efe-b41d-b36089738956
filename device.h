// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR BSD-3-Clause

#ifndef DEVICE_H
#define DEVICE_H

#include "characteristicinfo.h"
#include "deviceinfo.h"
#include "serviceinfo.h"

#include <QtBluetooth/qbluetoothdevicediscoveryagent.h>
#include <QtBluetooth/qlowenergycontroller.h>
#include <QtBluetooth/qlowenergyservice.h>

#include <QtCore/qlist.h>
#include <QtCore/qobject.h>
#include <QtCore/qvariant.h>

#include <QtQmlIntegration/qqmlintegration.h>

#include "bledevice.h"
#include "a830device.h"
#include "o80xdevice.h"



QT_BEGIN_NAMESPACE
class QBluetoothDeviceInfo;
class QBluetoothUuid;
QT_END_NAMESPACE

class Device: public QObject
{
    Q_OBJECT
    Q_PROPERTY(QVariant devicesList READ getDevices NOTIFY devicesUpdated)
    Q_PROPERTY(QVariant filteredDevicesList READ getFilteredDevices NOTIFY filteredDevicesUpdated)
    Q_PROPERTY(QVariant servicesList READ getServices NOTIFY servicesUpdated)
    Q_PROPERTY(QVariant characteristicList READ getCharacteristics NOTIFY characteristicsUpdated)
    Q_PROPERTY(QString update READ getUpdate WRITE setUpdate NOTIFY updateChanged)
    Q_PROPERTY(bool useRandomAddress READ isRandomAddress WRITE setRandomAddress
               NOTIFY randomAddressChanged)
    Q_PROPERTY(bool state READ state NOTIFY stateChanged)
    Q_PROPERTY(bool controllerError READ hasControllerError)

    // end
    QML_ELEMENT
    QML_SINGLETON

public:
    Device();
    ~Device();
    QVariant getDevices();
    QVariant getFilteredDevices();
    QVariant getServices();
    QVariant getCharacteristics();
    QString getUpdate();
    bool state();
    bool hasControllerError() const;

    bool isRandomAddress() const;
    void setRandomAddress(bool newValue);

public slots:
    void startDeviceDiscovery();
    void stopDeviceDiscovery();
    void scanServices(const QString &address);

    void connectToService(const QString &uuid);
    void disconnectFromDevice();

    //my
    void sendToBle(QString command, QString data);
    void sendToO80x(QString command, QString data, bool dont_send);
    void reconnectToA830();
    void setDevFilter(int filter);

private slots:
    // QBluetoothDeviceDiscoveryAgent related
    void addDevice(const QBluetoothDeviceInfo&);
    void deviceScanFinished();
    void deviceScanError(QBluetoothDeviceDiscoveryAgent::Error);

    // QLowEnergyController realted
    void addLowEnergyService(const QBluetoothUuid &uuid);
    void deviceConnected();
    void errorReceived(QLowEnergyController::Error);
    void serviceScanDone();
    void deviceDisconnected();

    // QLowEnergyService related
    void serviceDetailsDiscovered(QLowEnergyService::ServiceState newState);
    void filteredDevicesUpdatedSlot();

Q_SIGNALS:
    void devicesUpdated();
    void filteredDevicesUpdated();

    void servicesUpdated();
    void characteristicsUpdated();
    void updateChanged();
    void stateChanged();
    void disconnected();
    void randomAddressChanged();

    //my
    void logToApp(const QString &message, const QString &color);
    void serviceScanCompleted();
    void guid_data_changed(const QString &data_type, const QString &data);



//    void getTxData();

private:
    static void logTo(const QString &message, const QString &color);
    static void guid_data_changed_emitter(const QString &data_type, const QString &data);
    void setUpdate(const QString &message);
    QBluetoothDeviceDiscoveryAgent *discoveryAgent;
    DeviceInfo currentDevice;
    QList<DeviceInfo *> devices;
    QList<DeviceInfo *> filteredDevices;
    QList<ServiceInfo *> m_services;
    QList<CharacteristicInfo *> m_characteristics;
    QString m_previousAddress;
    QString m_message;
    bool connected = false;
    QLowEnergyController *controller = nullptr;
    bool m_deviceScanState = false;
    bool randomAddress = false;
    void charRead(const QLowEnergyCharacteristic &info, const QByteArray &value);

    // my

public:
    QString a830_addr;

private:
    // Device-specific members
    BleDevice *currentBleDevice;
    A830Device *a830Device;
    O80xDevice *o80xDevice;

    // Helper method to determine device type
    BleDevice* getDeviceForName(const QString &deviceName);



private:
    const QString SYS_LOG_PREFIX = "- ";
    const QString SYS_LOG_COLOR = "lightgreen";
    const QString BLE_IN_LOG_PREFIX = "<< ";
    const QString BLE_IN_LOG_COLOR = "lightblue";
    const QString BLE_OUT_LOG_PREFIX = ">> ";
    const QString BLE_OUT_LOG_COLOR = "lightblue";

    void txCharChanged(const QLowEnergyCharacteristic &c,
                       const QByteArray &value);
    QLowEnergyDescriptor mm_notificationDesc;
    void serviceStateChanged(QLowEnergyService::ServiceState s);
    void confirmedDescriptorWrite(const QLowEnergyDescriptor &d, const QByteArray &value);
    void subscribe_for_status_and_TX_chars_notifications();

    int filter_id = 0;
};

#endif // DEVICE_H

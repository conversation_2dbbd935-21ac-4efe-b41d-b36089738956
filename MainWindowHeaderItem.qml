import QtQuick
import QtQuick.Layouts

Item {
    property string labelName: ""
    property string labelValue: ""
    property int text_size: 12
    height: 38

    RowLayout {
    Text {
        id: name_text
        font.pointSize: text_size

//        width: contentWidth
        width: parent.width/2
        height: parent.height

//        anchors.verticalCenter: parent.verticalCenter
//        anchors.left: parent.left
        verticalAlignment: Text.AlignVCenter
        text: labelName

    }

    Text {
        font.pointSize: text_size

//        width: contentWidth
        width: parent.width/2
        height: parent.height

//        anchors.left: name_text.right
//        anchors.leftMargin: 10
        verticalAlignment: Text.AlignVCenter

        text: labelValue
    }
    }
}

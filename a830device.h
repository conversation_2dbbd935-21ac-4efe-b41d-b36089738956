#ifndef A830DEVICE_H
#define A830DEVICE_H

#include "bledevice.h"



class A830Device : public BleDevice
{
    Q_OBJECT

public:
    A830Device();
    ~A830Device() override;

    // A830-specific methods
    void handleCharacteristicChanged(const QLowEnergyCharacteristic &c, const QByteArray &value) override;
    void handleCharacteristicRead(const QLowEnergyCharacteristic &info, const QByteArray &value) override;
    void telemReadCb(const QByteArray &value);

private:
    void parse_telemetry(const QByteArray &value);
    void charReadCb(const QByteArray &value);
};

#endif // A830DEVICE_H

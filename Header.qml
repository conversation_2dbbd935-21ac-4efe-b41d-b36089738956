// Copyright (C) 2013 BlackBerry Limited. All rights reserved.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR BSD-3-Clause

import QtQuick

Rectangle {
    id: header
    width: parent.width
    height: 50
    border.width: 0
    color: "#a2dafc"
//    border.color: "#363636"
    radius: 0
    property string headerText: ""

    Text {
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        anchors.fill: parent
        text: header.headerText
        font.bold: true
        font.pointSize: 16
        elide: Text.ElideMiddle
        color: "#363636"
    }
}

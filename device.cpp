// Copyright (C) 2013 BlackBerry Limited. All rights reserved.
// Copyright (C) 2017 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR BSD-3-Clause

#include "device.h"

#include <QtBluetooth/qbluetoothdeviceinfo.h>
#include <QtBluetooth/qbluetoothuuid.h>

#include <QtCore/qdebug.h>
#include <QtCore/qmetaobject.h>
#include <QtCore/qtimer.h>
#include <QByteArray>

Device *device_ptr = NULL;

QString wifi_ssid = "";
QString wifi_pswd = "";
QString mqtt_uid = "";
QString mqtt_pswd = "";
QString blcr_link = "";
using namespace Qt::StringLiterals;



Device::Device()
{
    //! [les-devicediscovery-1]
    discoveryAgent = new QBluetoothDeviceDiscoveryAgent(this);
    discoveryAgent->setLowEnergyDiscoveryTimeout(25000);
    connect(discoveryAgent, &QBluetoothDeviceDiscoveryAgent::deviceDiscovered,
            this, &Device::addDevice);
    connect(discoveryAgent, &QBluetoothDeviceDiscoveryAgent::errorOccurred,
            this, &Device::deviceScanError);
    connect(discoveryAgent, &QBluetoothDeviceDiscoveryAgent::finished,
            this, &Device::deviceScanFinished);
    connect(discoveryAgent, &QBluetoothDeviceDiscoveryAgent::canceled,
            this, &Device::deviceScanFinished);
    connect(this, &Device::devicesUpdated, this, &Device::filteredDevicesUpdatedSlot);
    //! [les-devicediscovery-1]

    setUpdate(u"Search"_s);

    device_ptr = this;

    // Initialize device-specific classes
    a830Device = new A830Device();
    o80xDevice = new O80xDevice();
    currentBleDevice = nullptr;

    // Set up callbacks
    BleDevice::setLogCallback(Device::logTo);
    BleDevice::setDataCallback(Device::guid_data_changed_emitter);

    a830_addr = "-";

//    deviceData = new DeviceStatusData();

}



Device::~Device()
{
    qDeleteAll(devices);
    qDeleteAll(m_services);
    qDeleteAll(m_characteristics);
    devices.clear();
    m_services.clear();
    m_characteristics.clear();

    // Clean up device-specific classes
    delete a830Device;
    delete o80xDevice;
}

void Device::startDeviceDiscovery()
{
    qDeleteAll(devices);
    devices.clear();
    emit devicesUpdated();

    //! [les-devicediscovery-2]
    discoveryAgent->start(QBluetoothDeviceDiscoveryAgent::LowEnergyMethod);
    //! [les-devicediscovery-2]

    if (discoveryAgent->isActive()) {
        setUpdate(u"Stop"_s);
        m_deviceScanState = true;
        Q_EMIT stateChanged();
    }
}

void Device::stopDeviceDiscovery()
{
    if (discoveryAgent->isActive())
        discoveryAgent->stop();
}



//! [les-devicediscovery-3]
void Device::addDevice(const QBluetoothDeviceInfo &info)
{
    if (info.coreConfigurations() & QBluetoothDeviceInfo::LowEnergyCoreConfiguration) {
        auto devInfo = new DeviceInfo(info);
        auto it = std::find_if(devices.begin(), devices.end(),
                               [devInfo](DeviceInfo *dev) {
                                   return devInfo->getAddress() == dev->getAddress();
                               });
        if (it == devices.end()) {
            devices.append(devInfo);
        } else {
            auto oldDev = *it;
            *it = devInfo;
            delete oldDev;
        }
        emit devicesUpdated();
    }
}
//! [les-devicediscovery-3]

void Device::deviceScanFinished()
{
    m_deviceScanState = false;
    emit stateChanged();
    if (devices.isEmpty())
        setUpdate(u"No Low Energy devices found..."_s);
    else
        setUpdate(u"Done! Scan Again!"_s);
}

QVariant Device::getDevices()
{
    return QVariant::fromValue(devices);
}

QVariant Device::getFilteredDevices()
{
    return QVariant::fromValue(filteredDevices);
}

void Device::filteredDevicesUpdatedSlot()
{
    if(devices.isEmpty())
    {
        filteredDevices.clear();
        emit filteredDevicesUpdated();
    } else {
        qDebug() << devices.last()->getName();

        if (filter_id == 0 ||
            (filter_id == 1 && devices.last()->getName() == "A830") ||
            (filter_id == 2 && devices.last()->getName() == "A630") ||
            (filter_id == 3 && devices.last()->getName() == "O801") ||
            (filter_id == 3 && devices.last()->getName() == "O802") ||
            (filter_id == 3 && devices.last()->getName() == "O80x"))
        {
            if(!filteredDevices.contains(devices.last()))
            {
                DeviceInfo *d = new DeviceInfo();
                d->setDevice(devices.last()->getDevice());
//                filteredDevices.append(devices.last());
                filteredDevices.append(d);
//                qDebug() << "appended";
//                qDebug() << d->getAddress();
                emit filteredDevicesUpdated();
            }
        }
    }

//    emit filteredDevicesUpdated();
}

QVariant Device::getServices()
{
    return QVariant::fromValue(m_services);
}

QVariant Device::getCharacteristics()
{
    return QVariant::fromValue(m_characteristics);
}

QString Device::getUpdate()
{
    return m_message;
}



void Device::reconnectToA830()
{
    if (!controller) return;

    // if connected - disconnect
    if (controller->state() != QLowEnergyController::UnconnectedState)
    {
        disconnectFromDevice();
        return;
    }

    // if not connected - connect
    if (a830_addr == "-") return;
    scanServices(a830_addr);
}


void Device::setDevFilter(int filter)
{
    qDebug() << filter;
    filter_id = filter;
}



void Device::scanServices(const QString &address)
{
    a830_addr = address;
    // We need the current device for service discovery.

    for (auto d: std::as_const(devices)) {
        if (auto device = qobject_cast<DeviceInfo *>(d)) {
            if (device->getAddress() == address) {
                currentDevice.setDevice(device->getDevice());
                break;
            }
        }
    }

    if (!currentDevice.getDevice().isValid()) {
        qWarning() << "Not a valid device";
        return;
    }

    // Set the current device based on the device name
    currentBleDevice = getDeviceForName(currentDevice.getName());

    qDeleteAll(m_characteristics);
    m_characteristics.clear();
    emit characteristicsUpdated();
    qDeleteAll(m_services);
    m_services.clear();
    emit servicesUpdated();

    setUpdate(u"Back\n(Connecting to device...)"_s);

    if (controller && m_previousAddress != currentDevice.getAddress()) {
        controller->disconnectFromDevice();
        delete controller;
        controller = nullptr;
    }

    //! [les-controller-1]
    if (!controller) {
        // Connecting signals and slots for connecting to LE services.
        controller = QLowEnergyController::createCentral(currentDevice.getDevice(), this);
                connect(controller, &QLowEnergyController::connected,
                this, &Device::deviceConnected);
        connect(controller, &QLowEnergyController::errorOccurred, this, &Device::errorReceived);
        connect(controller, &QLowEnergyController::disconnected,
                this, &Device::deviceDisconnected);
        connect(controller, &QLowEnergyController::serviceDiscovered,
                this, &Device::addLowEnergyService);
        connect(controller, &QLowEnergyController::discoveryFinished,
                this, &Device::serviceScanDone);
    }

    if (isRandomAddress())
        controller->setRemoteAddressType(QLowEnergyController::RandomAddress);
    else
        controller->setRemoteAddressType(QLowEnergyController::PublicAddress);
    controller->connectToDevice();
    //! [les-controller-1]

    m_previousAddress = currentDevice.getAddress();
}

void Device::addLowEnergyService(const QBluetoothUuid &serviceUuid)
{
    //! [les-service-1]
    QLowEnergyService *service = controller->createServiceObject(serviceUuid);
    if (!service) {
        qWarning() << "Cannot create service for uuid";
        return;
    }
    //! [les-service-1]
    auto serv = new ServiceInfo(service);
    m_services.append(serv);

    emit servicesUpdated();

    // check if its control service
    qWarning() << "found service: " << serv->getUuid();
    if (serv->getUuid() == "0xf3"){
        qWarning() << "Control service found";
        emit logToApp(SYS_LOG_PREFIX + "found control service: " + serv->getUuid(), SYS_LOG_COLOR);

        if (currentBleDevice)
        {
            currentBleDevice->setControlService(serv);
        }

        connect(serv->service(), &QLowEnergyService::stateChanged, this, &Device::serviceStateChanged);

        connect(serv->service(), &QLowEnergyService::characteristicChanged, this, &Device::txCharChanged);
        qWarning() << "connected char change to handler --- ";

        connect(serv->service(), &QLowEnergyService::characteristicRead, this, &Device::charRead);

        connect(serv->service(), &QLowEnergyService::descriptorWritten, this, &Device::confirmedDescriptorWrite);
    }
}
//! [les-service-1]

void Device::serviceScanDone()
{
    setUpdate(u"Back\n(Service scan done!)"_s);
    // force UI in case we didn't find anything
    if (m_services.isEmpty())
        emit servicesUpdated();
    qWarning() << "service scan done";
    emit serviceScanCompleted();
}

void Device::connectToService(const QString &uuid)
{
    qWarning() << "connecting to service.. " << uuid;
    QLowEnergyService *service = nullptr;
    for (auto s: std::as_const(m_services)) {
        auto serviceInfo = qobject_cast<ServiceInfo *>(s);
        if (!serviceInfo)
            continue;

        if (serviceInfo->getUuid() == uuid) {
            service = serviceInfo->service();
            qWarning() << "found service in the list";
            break;
        }
    }

    if (!service)
    {
        qWarning() << "service not found";
        return;
    }


    qDeleteAll(m_characteristics);
    m_characteristics.clear();
    emit characteristicsUpdated();

    if (service->state() == QLowEnergyService::RemoteService) {
        qWarning() << "called service->discoverDetails()";
        //! [les-service-3]
        connect(service, &QLowEnergyService::stateChanged,
                this, &Device::serviceDetailsDiscovered);
        service->discoverDetails();
        setUpdate(u"Back\n(Discovering details...)"_s);
        //! [les-service-3]
        return;
    }

    //discovery already done
    qWarning() << "discovery already done";
    const QList<QLowEnergyCharacteristic> chars = service->characteristics();
    for (const QLowEnergyCharacteristic &ch : chars) {
        auto cInfo = new CharacteristicInfo(ch);
        m_characteristics.append(cInfo);
    }

    QTimer::singleShot(0, this, &Device::characteristicsUpdated);
}

void Device::deviceConnected()
{
    setUpdate(u"Back\n(Discovering services...)"_s);
    connected = true;
    //! [les-service-2]
    emit logToApp(SYS_LOG_PREFIX + "connected to device", SYS_LOG_COLOR);
    guid_data_changed_emitter("connection_status","connected");
    controller->discoverServices();
    //! [les-service-2]
}

void Device::errorReceived(QLowEnergyController::Error /*error*/)
{
    qWarning() << "Error: " << controller->errorString();
    setUpdate(u"Back\n(%1)"_s.arg(controller->errorString()));
}

void Device::setUpdate(const QString &message)
{
    m_message = message;
    emit updateChanged();
}

void Device::disconnectFromDevice()
{
    // UI always expects disconnect() signal when calling this signal
    // TODO what is really needed is to extend state() to a multi value
    // and thus allowing UI to keep track of controller progress in addition to
    // device scan progress

    if (controller->state() != QLowEnergyController::UnconnectedState)
        controller->disconnectFromDevice();
    else
        deviceDisconnected();
}

void Device::deviceDisconnected()
{
    qWarning() << "Disconnected from device";

    // Reset device-specific classes
    if (currentBleDevice)
    {
        currentBleDevice->setControlService(nullptr);
        currentBleDevice->setRxChar(nullptr);
        currentBleDevice->setTxChar(nullptr);
        currentBleDevice = nullptr;
    }

    guid_data_changed_emitter("connection_status","disconnected");
    emit logToApp(SYS_LOG_PREFIX + "disconnected from device", SYS_LOG_COLOR);
    emit disconnected();
}



void Device::serviceDetailsDiscovered(QLowEnergyService::ServiceState newState)
{
    qWarning() << "service detailes descovered: " << newState;
    if (newState != QLowEnergyService::RemoteServiceDiscovered) {
        // do not hang in "Scanning for characteristics" mode forever
        // in case the service discovery failed
        // We have to queue the signal up to give UI time to even enter
        // the above mode
        if (newState != QLowEnergyService::RemoteServiceDiscovering) {
            QMetaObject::invokeMethod(this, "characteristicsUpdated",
                                      Qt::QueuedConnection);
        }
        return;
    }

    auto service = qobject_cast<QLowEnergyService *>(sender());
    if (!service)
        return;

    //! [les-chars]
    const QList<QLowEnergyCharacteristic> chars = service->characteristics();
    for (const QLowEnergyCharacteristic &ch : chars) {
        auto cInfo = new CharacteristicInfo(ch);
        m_characteristics.append(cInfo);
        qWarning() << "found charachteristic: " << cInfo->getUuid() << cInfo->getCharacteristic().uuid();

        if (ch.isValid()){
//        QLowEnergyDescriptor m_notif = ch.descriptor(QBluetoothUuid::DescriptorType::ClientCharacteristicConfiguration); //   descriptor(QBluetoothUuid::DescriptorType::UnknownDescriptorType);
//        if (m_notif.isValid())
//        {
//            qWarning() << " ---- decriptor valid --------- ";
//            service->writeDescriptor(m_notif, QByteArray::fromHex("0100"));
//            qWarning() << " ---- wrote descriptor --------- ";

//        } else {
//            qWarning() << " ---- decriptor invalid --------- ";
//        }
        }

        if (cInfo->getUuid() == "0xf301")
        {
            emit logToApp("- found device name char.:  " + cInfo->getUuid(), SYS_LOG_COLOR);
            service->readCharacteristic(cInfo->getCharacteristic());
        }
        if (cInfo->getUuid() == "0xf302")
        {
            emit logToApp("- found firmware version char.:  " + cInfo->getUuid(), SYS_LOG_COLOR);
            service->readCharacteristic(cInfo->getCharacteristic());
        }
        if (cInfo->getUuid() == "0xf303")
        {
            qWarning() << "found RX charx";
            emit logToApp("- found RX char.:  " + cInfo->getUuid(), SYS_LOG_COLOR);

            if (currentBleDevice)
            {
                currentBleDevice->setRxChar(cInfo);
            }

            // writes to char upn discovery - to test
            //            QByteArray *arr = new QByteArray(10,'r');
            //            qWarning("sending data to ble");
            //            service->writeCharacteristic(ch, *arr ,QLowEnergyService::WriteWithResponse);
            //            free (arr);
        }

        if (cInfo->getUuid() == "0xf304")
        {
            qWarning() << "found TX charx";
            emit logToApp("- found TX char.:  " + cInfo->getUuid(), SYS_LOG_COLOR);

            if (currentBleDevice)
            {
                currentBleDevice->setTxChar(cInfo);
            }

            if (ch.isValid())
            {
                QLowEnergyDescriptor m_notif = ch.clientCharacteristicConfiguration();
                if (m_notif.isValid())
                {
                    qWarning() << "    decriptor valid";
                } else {
                    qWarning() << "    Error! decriptor invalid";
                }
            }
        }

    }
    //! [les-chars]

    emit characteristicsUpdated();

    subscribe_for_status_and_TX_chars_notifications();

//  GATTS_CHAR_RX           0xF301
//  GATTS_CHAR_TX           0xF302
//  GATTS_CHAR_FIRM_VER     0xF303
//  GATTS_CHAR_DEVICE_TYPE  0xF304
}

void Device::deviceScanError(QBluetoothDeviceDiscoveryAgent::Error error)
{
    if (error == QBluetoothDeviceDiscoveryAgent::PoweredOffError) {
        setUpdate(u"The Bluetooth adaptor is powered off, power it on before doing discovery."_s);
    } else if (error == QBluetoothDeviceDiscoveryAgent::InputOutputError) {
        setUpdate(u"Writing or reading from the device resulted in an error."_s);
    } else {
        static QMetaEnum qme = discoveryAgent->metaObject()->enumerator(
                    discoveryAgent->metaObject()->indexOfEnumerator("Error"));
        setUpdate(u"Error: "_s + QLatin1StringView(qme.valueToKey(error)));
    }

    m_deviceScanState = false;
    emit stateChanged();
}

bool Device::state()
{
    return m_deviceScanState;
}

bool Device::hasControllerError() const
{
    return (controller && controller->error() != QLowEnergyController::NoError);
}

bool Device::isRandomAddress() const
{
    return randomAddress;
}

void Device::setRandomAddress(bool newValue)
{
    randomAddress = newValue;
    emit randomAddressChanged();
}

BleDevice* Device::getDeviceForName(const QString &deviceName)
{
    if (deviceName == "O80x" || deviceName == "O801" || deviceName == "O802")
    {
        return o80xDevice;
    }
    else
    {
        return a830Device;
    }
}



/***************************************
 *
 *             my functions
 *
 **************************************/

void Device::sendToBle(QString command, QString data)
{
    if (currentBleDevice)
    {
        qDebug() << "sending cmd: "+ command + " data: " +data;
        currentBleDevice->sendCommandToDevice(command, data);

        if (command == "wifi_ssid")
        {
            wifi_ssid = data;
        } else if (command == "wifi_pswd")
        {
            wifi_pswd = data;
        } else if (command == "mqtt_uid") {
            mqtt_uid = data;
        } else if (command == "mqtt_psw") {
            mqtt_pswd = data;
        } else if (command == "blcr_link") {
            blcr_link = data;
        }
    }
    else
    {
        qDebug() << "Not connected to any device";
    }
}



void Device::sendToO80x(QString command, QString data, bool dont_send)
{
    qDebug() << "sending cmd to o80x: "+ command + " data: " +data;

    if (currentBleDevice == o80xDevice)
    {
        o80xDevice->sendHeaterCommandToDevice(command, data, dont_send);
    }
    else
    {
        qDebug() << "Not connected to O80x device";
    }
}



void Device::txCharChanged(const QLowEnergyCharacteristic &c, const QByteArray &value)
{
    if (currentBleDevice)
    {
        if(c.uuid().toString() == "{0000f304-0000-1000-8000-00805f9b34fb}")
        {
            if(value.toHex(' ') == "aa 10 00 00")    // setting wifi ssid
            {
                QByteArray arr;
                arr.append(0x11);
                arr.append(0x01);
                arr.append(wifi_ssid.toUtf8().size());
                arr.append(wifi_ssid.toUtf8());
                currentBleDevice->sendRawDataToDevice(arr);
                return;
            } else if(value.toHex(' ')    == "aa 11 01 00"){  // finish setting wifi ssid
                currentBleDevice->sendRawDataToDevice(QByteArray::fromHex("12"));
                return;



            } else if (value.toHex(' ') == "aa 20 00 00"){  // setting wifi pswd
                QByteArray arr;
                arr.append(0x21);
                arr.append(0x01);
                arr.append(wifi_pswd.toUtf8().size());
                arr.append(wifi_pswd.toUtf8());
                currentBleDevice->sendRawDataToDevice(arr);
                return;
            } else if (value.toHex(' ')    == "aa 21 01 00"){  // finish setting wifi pswd
                currentBleDevice->sendRawDataToDevice(QByteArray::fromHex("22"));
                return;



            } else if (value.toHex(' ') == "aa 60 00 00"){  // setting mqtt uid
                QByteArray arr;
                arr.append(0x61);
                arr.append(0x01);
                arr.append(mqtt_uid.toUtf8().size());
                arr.append(mqtt_uid.toUtf8());
                currentBleDevice->sendRawDataToDevice(arr);
                return;
            } else if (value.toHex(' ') == "aa 61 01 00"){  // connect to shelly
                currentBleDevice->sendRawDataToDevice(QByteArray::fromHex("62"));
                return;



            } else if (value.toHex(' ') == "aa 50 00 00"){  // setting mqtt uid
                QByteArray arr;
                arr.append(0x51);
                arr.append(0x01);
                arr.append(mqtt_pswd.toUtf8().size());
                arr.append(mqtt_pswd.toUtf8());
                currentBleDevice->sendRawDataToDevice(arr);
                return;
            } else if (value.toHex(' ') == "aa 51 01 00"){  // connect to shelly
                currentBleDevice->sendRawDataToDevice(QByteArray::fromHex("52"));
                return;



            } else if (value.toHex(' ') == "aa 40 00 00"){  // setting mqtt uid
                QByteArray arr;
                arr.append(0x41);
                arr.append(0x01);
                arr.append(blcr_link.toUtf8().size());
                arr.append(blcr_link.toUtf8());
                currentBleDevice->sendRawDataToDevice(arr);
                return;
            } else if (value.toHex(' ') == "aa 41 01 00"){  // connect to shelly
                currentBleDevice->sendRawDataToDevice(QByteArray::fromHex("42"));
                return;
            }
        }
        currentBleDevice->handleCharacteristicChanged(c, value);
    }
}



void Device::charRead(const QLowEnergyCharacteristic &info, const QByteArray &value)
{
    if (currentBleDevice)
    {
        // For device name characteristic, we need to pass the device name
        if (info.uuid().toString() == "{0000f301-0000-1000-8000-00805f9b34fb}")
        {
            // Store the device name in the currentBleDevice
        }

        currentBleDevice->handleCharacteristicRead(info, value);
    }
}



void Device::serviceStateChanged(QLowEnergyService::ServiceState s)
{
    switch (s) {
    case QLowEnergyService::RemoteServiceDiscovering:
        break;

    case QLowEnergyService::RemoteServiceDiscovered:
    {
        QLowEnergyService *service = nullptr;
        for (auto s: std::as_const(m_services)) {
            auto serviceInfo = qobject_cast<ServiceInfo *>(s);
            if (!serviceInfo)
                continue;

            if (serviceInfo->getUuid() == "0xf3") {
                service = serviceInfo->service();
                qWarning() << "found A830 service";
                break;
            }
        }

        if (!service)
        {
            qWarning()<< "no services found";
            return;
        }

        const QLowEnergyCharacteristic hrChar = service->characteristic(QBluetoothUuid("0000f302-0000-1000-8000-00805f9b34fb"));
        if (!hrChar.isValid())
        {
            qWarning() << "no char found =====";
            break;
        }
        qWarning() << "char found =====";

        mm_notificationDesc = hrChar.descriptor(QBluetoothUuid::DescriptorType::ClientCharacteristicConfiguration);
        if (mm_notificationDesc.isValid()) {
            service->writeDescriptor(mm_notificationDesc, QByteArray::fromHex("0100"));
        }
        break;
    }

    default:
        break;
    }
}

void Device::confirmedDescriptorWrite(const QLowEnergyDescriptor &d, const QByteArray &value)
{
    qWarning()<< "confirm Decr Write called ==========" << value;
}



void Device::logTo(const QString &message, const QString &color)
{
    emit device_ptr->logToApp(message, color);
}



void Device::guid_data_changed_emitter(const QString &data_type, const QString &data)
{
    emit device_ptr->guid_data_changed(data_type, data);
}









void Device::subscribe_for_status_and_TX_chars_notifications()
{
    QLowEnergyService * bork_service = NULL;
    QLowEnergyCharacteristic status_char;
    QLowEnergyCharacteristic TX_char;
    QLowEnergyCharacteristic Telem_char;
    bool char_found = false;
    bool tx_char_found = false;
    bool telem_char_found = false;

    for (auto s: std::as_const(m_services)) {
        auto serviceInfo = qobject_cast<ServiceInfo *>(s);
        if (!serviceInfo)
            continue;
        if (serviceInfo->getUuid() == "0xf3") {
            bork_service = serviceInfo->service();
        }
//        qDebug() << serviceInfo->getUuid();
    }

    for (auto c: std::as_const(m_characteristics)) {
        auto charInfo = qobject_cast<CharacteristicInfo *>(c);
        if (!charInfo)
            continue;
//        qDebug() << charInfo->getUuid();

        if (charInfo->getUuid() == "0xf305")
        {
            status_char = charInfo->getCharacteristic();
            char_found = true;
        }
        if (charInfo->getUuid() == "0xf304")
        {
            TX_char = charInfo->getCharacteristic();
            tx_char_found = true;
        }
        if (charInfo->getUuid() == "0xf306")
        {
            Telem_char = charInfo->getCharacteristic();
            telem_char_found = true;
        }
    }

    if (bork_service != NULL && char_found)
    {
        QLowEnergyDescriptor notification = status_char.descriptor(
            QBluetoothUuid::DescriptorType::ClientCharacteristicConfiguration);

        bork_service->writeDescriptor(notification, QByteArray::fromHex("0100"));
    }
    if (bork_service != NULL && tx_char_found)
    {
        QLowEnergyDescriptor notification = TX_char.descriptor(
            QBluetoothUuid::DescriptorType::ClientCharacteristicConfiguration);

        bork_service->writeDescriptor(notification, QByteArray::fromHex("0100"));
    }
    if (bork_service != NULL && telem_char_found)
    {
        QLowEnergyDescriptor notification = Telem_char.descriptor(
            QBluetoothUuid::DescriptorType::ClientCharacteristicConfiguration);

        bork_service->writeDescriptor(notification, QByteArray::fromHex("0100"));
    }
}




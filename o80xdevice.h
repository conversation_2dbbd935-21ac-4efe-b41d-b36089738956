#ifndef O80XDEVICE_H
#define O80XDEVICE_H

#include "bledevice.h"

class O80xDevice : public BleDevice
{
    Q_OBJECT

public:
    O80xDevice();
    ~O80xDevice() override;

    // O80x-specific methods
    void sendHeaterCommandToDevice(QString command, QString data, bool dont_send);
    void handleCharacteristicChanged(const QLowEnergyCharacteristic &c, const QByteArray &value) override;
    void handleCharacteristicRead(const QLowEnergyCharacteristic &info, const QByteArray &value) override;

private:
    void treatO80xTelemetry(const QByteArray &value);
};

#endif // O80XDEVICE_H

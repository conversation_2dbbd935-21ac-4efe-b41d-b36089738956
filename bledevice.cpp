#include "bledevice.h"

// Initialize static members
void (*BleDevice::logCallback)(const QString &message, const QString &color) = nullptr;
void (*BleDevice::dataCallback)(const QString &data_type, const QString &data) = nullptr;

BleDevice::BleDevice()
{
    m_service = nullptr;
    m_txChar = nullptr;
    m_rxChar = nullptr;

    // Initialize command-related data
    next_ssid_piece_to_send = 0;
    next_pswd_piece_to_send = 0;
    next_blcr_piece_to_send = 0;
    next_uid_piece_to_send = 0;
    next_mq_pswd_piece_to_send = 0;
}

BleDevice::~BleDevice()
{
    // Base class destructor
}

void BleDevice::setControlService(ServiceInfo *service)
{
    m_service = service;
}

ServiceInfo* BleDevice::getControlService()
{
    return m_service;
}

void BleDevice::setTxChar(const CharacteristicInfo *cr)
{
    m_txChar = cr;
}

const CharacteristicInfo* BleDevice::getTxChar()
{
    return m_txChar;
}

void BleDevice::setRxChar(const CharacteristicInfo *cr)
{
    m_rxChar = cr;
}

bool BleDevice::checkPointersNotNull()
{
    return (m_service != nullptr && m_txChar != nullptr && m_rxChar != nullptr);
}

void BleDevice::setLogCallback(void (*callback)(const QString &message, const QString &color))
{
    logCallback = callback;
}

void BleDevice::setDataCallback(void (*callback)(const QString &data_type, const QString &data))
{
    dataCallback = callback;
}

void BleDevice::log(const QString &message, const QString &color)
{
    if (logCallback)
        logCallback(message, color);
}

void BleDevice::updateData(const QString &data_type, const QString &data)
{
    if (dataCallback)
        dataCallback(data_type, data);
}

QString BleDevice::byteArrayToString(QByteArray arr)
{
    QString result;

    result = arr.toHex();
    for (int var = result.size(); var >= 0; var--) {
        if (var % 2 == 0)
            result.insert(var, ' ');
    }

    if (arr.size() < 2) return result;

    // Special handling for certain commands
    if (arr[1] == 0x70 || (uint8_t)arr[1] == 0xF5 || (uint8_t)arr[1] == 0xF9) {
        // For these commands, reply is a string
        result.append("     ").append(QString(arr).remove(0, 3));
    }

    return result;
}

void BleDevice::sendCommandToDevice(QString command, QString data)
{
    if (!checkPointersNotNull())
    {
        log(" Device not connected", BLE_OUT_LOG_COLOR);
        return;
    }

    // Map string commands to enum values
    static const QMap<QString, int> commandMap = {
        {"wifi_ssid", CMD_SET_WIFI_SSID},
        {"wifi_pswd", CMD_SET_WIFI_PSWD},
        {"blcr_link", CMD_SET_BLCR_LINK},
        {"mqtt_uid", CMD_MQTT_UID},
        {"mqtt_psw", CMD_MQTT_PSWD},
        {"get_device_name", CMD_SEND_NAME},
        {"get_device_id", CMD_SEND_ID},
        {"get_guid", TODO_CMD_GET_GUID},
        {"get_challenge", CMD_SEND_CHALLENGE},
        {"get_challenge_sign", ACTION_GET_CHALLENGE},
        {"get_broker_link", TODO_CMD_GET_BROKER_LINK},
        {"conn_s_h", CMD_CONN_SH},
        {"conn_wifi", CMD_CONN_WIFI},
        {"conn_blcr", CMD_CONN_BLCR},
        {"conn_broker", CMD_CONN_BROKER},
        {"send_to_broker", TODO_CMD_SEND_TO_BROKER},
        {"fake_from_broker", CMD_FAKE_FROM_BROKER},
        {"uart_pkt", CMD_SEND_TO_UART},
        {"reset", CMD_RESET},
        {"run_test", CMD_START_TEST},
        {"ota", CMD_OTA_HARDCODED_LINK},
        {"ota_with_link", CMD_OTA},
        {"emulator", CMD_EMULATOR},
        {"turn_logs", ACTION_ENABLE_LOGS},
        {"ota_cert_on", CMD_DISABLE_OTA_CERT},
        {"send_certificate", CMD_SEND_CERTIFICATE},
    };

    // Get the command type from the map
    int cmdType = commandMap.value(command, CMD_UNKNOWN);

    QByteArray arr;

    // Switch on the command type
    switch (cmdType) {
    case CMD_SET_WIFI_SSID:
        arr.append(CMD_SET_WIFI_SSID);
        arr.append(data.size());
        arr.append('\0');
        arr.append(data.size()%20 == 0 ? data.size()/20 : data.size()/20 + 1);
        if (data.size() >= 20){
            arr.append(20);
        } else {
            arr.append(data.size());
        }

        next_ssid_piece_to_send = 1;
        ssid = QString(data);
        break;

    case CMD_SET_WIFI_PSWD:
        arr.append(CMD_SET_WIFI_PSWD);
        arr.append(data.size()%255);
        arr.append(data.size()/255);
        arr.append(data.size()%20 == 0 ? data.size()/20 : data.size()/20 + 1);
        if (data.size() >= 20){
            arr.append(20);
        } else {
            arr.append(data.size());
        }

        next_pswd_piece_to_send = 1;
        wifi_pswd = QString(data);
        break;

    case CMD_SET_BLCR_LINK:
        arr.append(CMD_SET_BLCR_LINK);
        arr.append(data.size()%255);
        arr.append(data.size()/255);
        arr.append(data.size()%20 == 0 ? data.size()/20 : data.size()/20 + 1);
        if (data.size() >= 20){
            arr.append(20);
        } else {
            arr.append(data.size());
        }

        next_blcr_piece_to_send = 1;
        blcr_link = QString(data);
        break;

    case CMD_MQTT_UID:
        arr.append(CMD_MQTT_UID);
        arr.append(data.size()%255);
        arr.append(data.size()/255);
        arr.append(data.size()%20 == 0 ? data.size()/20 : data.size()/20 + 1);
        if (data.size() >= 20){
            arr.append(20);
        } else {
            arr.append(data.size());
        }

        next_uid_piece_to_send = 1;
        mqtt_uid = QString(data);
        break;

    case CMD_MQTT_PSWD:
        arr.append(CMD_MQTT_PSWD);
        arr.append(data.size()%255);
        arr.append(data.size()/255);
        arr.append(data.size()%20 == 0 ? data.size()/20 : data.size()/20 + 1);
        if (data.size() >= 20){
            arr.append(20);
        } else {
            arr.append(data.size());
        }

        next_mq_pswd_piece_to_send = 1;
        mqtt_pswd = QString(data);
        break;

    case CMD_SEND_NAME:
        arr.append(CMD_SEND_NAME);
        break;

    case CMD_SEND_ID:
        arr.append(CMD_SEND_ID);
        break;

    case TODO_CMD_GET_GUID:
        // TODO
        break;

    case CMD_SEND_CHALLENGE:
        arr.append(CMD_SEND_CHALLENGE);
        break;

    case ACTION_GET_CHALLENGE:
        // arr.append(CMD_SEND_CHALLENGE_SIGN_SIZE);
        // TODO
        break;

    case TODO_CMD_GET_BROKER_LINK:
        // TODO
        break;

    case CMD_CONN_SH:
        arr.append(CMD_CONN_SH);
        break;

    case CMD_CONN_WIFI:
        arr.append(CMD_CONN_WIFI);
        break;

    case CMD_CONN_BLCR:
        arr.append(CMD_CONN_BLCR);
        break;

    case CMD_CONN_BROKER:
        arr.append(CMD_CONN_BROKER);
        break;

    case TODO_CMD_SEND_TO_BROKER:
        // TODO
        break;

    case CMD_FAKE_FROM_BROKER:
        arr.append(CMD_FAKE_FROM_BROKER);
        data.replace(" ","");
        arr.append(data.length()/2);
        arr.append(QByteArray::fromHex(data.toUtf8()));
        break;

    case CMD_SEND_TO_UART:
        arr.append(CMD_SEND_TO_UART);
        data.replace(" ","");
        arr.append(data.length()/2);
        arr.append(QByteArray::fromHex(data.toUtf8()));
        break;

    case CMD_RESET:
        arr.append(CMD_RESET);
        arr.append(0xF2);
        arr.append(0xF3);
        break;

    case CMD_START_TEST:
        arr.append(CMD_START_TEST);
        break;

    case CMD_OTA_HARDCODED_LINK:
        arr.append(CMD_OTA_HARDCODED_LINK);
        break;

    case CMD_OTA:
        arr.append(CMD_OTA);
        arr.append(data.size()%255);
        arr.append(data.size()/255);
        arr.append(data.toUtf8(), data.size());
        break;

    case CMD_EMULATOR:
        if(data == "on")
        {
            arr.append(CMD_EMULATOR);
            arr.append(0x01);
        } else if(data == "off") {
            arr.append(CMD_EMULATOR);
            arr.append(0x0f);
        } else {
            qDebug() << "Unknown emulator command: " << data;
            return;
        }
        break;

    case ACTION_ENABLE_LOGS:
        {
            if(data == "on") {
                uint8_t cmd[] = {CMD_FAKE_FROM_BROKER, 0x0e, 1,1,'L','O','G', '\0', '\0', 1, '\0', '\0', 3, '\0','\0','\0'};
                arr.append(QByteArray(reinterpret_cast<char*>(cmd), 16));
            } else if(data == "off") {
                uint8_t cmd[] = {CMD_FAKE_FROM_BROKER, 0x0e, 1,1,'L','O','G', '\0', '\0', 1, '\0', '\0', 0, '\0','\0','\0'};
                arr.append(QByteArray(reinterpret_cast<char*>(cmd), 16));
            } else { 
                qDebug() << "Unknown logs command: " << data;
                return;
            }
        }
        break;

    case CMD_DISABLE_OTA_CERT: 
        if(data == "on")
        {
            arr.append(CMD_DISABLE_OTA_CERT);
            arr.append(0x00);
        } else if(data == "off") {
            arr.append(CMD_DISABLE_OTA_CERT);
            arr.append(0x01);
        } else {
            qDebug() << "Unknown cert command: " << data;
            return;
        }
        break;

    case CMD_SEND_CERTIFICATE:  /// TODO
        arr.append(CMD_SEND_CERTIFICATE);
        break;

    case CMD_UNKNOWN:
    default:
        // Handle unknown command
        qDebug() << "Unknown command:" << command;
        return;
    }

    log(BLE_OUT_LOG_PREFIX + command + "    " + byteArrayToString(arr), BLE_OUT_LOG_COLOR);
    m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), arr, QLowEnergyService::WriteWithResponse);
    qDebug() << "command sent: " << byteArrayToString(arr);
}



void BleDevice::sendRawDataToDevice(QByteArray data)
{
    m_service->service()->writeCharacteristic(m_rxChar->getCharacteristic(), data, QLowEnergyService::WriteWithResponse);
}

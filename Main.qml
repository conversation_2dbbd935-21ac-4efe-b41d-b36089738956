// Copyright (C) 2023 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR BSD-3-Clause

import QtQuick
import QtQuick.Layouts



Window {
    id: main

    width: Qt.platform.os === "android" ? 600 : pagesLayout.currentIndex === 3 ? 1200 : 400
    height: Qt.platform.os === "android" ? 640 : 800
    visible: true

    StackLayout {
        id: pagesLayout
        anchors.fill: parent
        currentIndex: 0

        Devices {
            onShowServices: (device_name) => {
                if (Qt.platform.os === "android")
                {
                    if (device_name === "O80x" || device_name === "O801" || device_name === "O802")
                    {
                        pagesLayout.currentIndex = 5
                    } else {

                        pagesLayout.currentIndex = 4
                    }
                } else if (device_name === "O80x" || device_name === "O801" || device_name === "O802") {
                    pagesLayout.currentIndex = 3
                } else {
                    pagesLayout.currentIndex = 3
                }

            }
        }

        Services {
            onShowDevices: pagesLayout.currentIndex = 0
            onShowCharacteristics: pagesLayout.currentIndex = 2
        }

        Characteristics {
            onShowDevices: pagesLayout.currentIndex = 0
            onShowServices: (device_name) => {
                                pagesLayout.currentIndex = 1
                            }
        }

        MainWindow {
            onShowDevices: pagesLayout.currentIndex = 0
        }

        AndroidMainWindow {
            onShowDevices: pagesLayout.currentIndex = 0
        }

        AndroidO80xWindow {
            onShowDevices: pagesLayout.currentIndex = 0
        }
    }
}

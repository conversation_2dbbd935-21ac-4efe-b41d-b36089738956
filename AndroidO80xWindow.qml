pragma ComponentBehavior: Bound
import QtQuick
import QtQuick.Layouts
import QtQml
//import QtQuick.Controls
import QtQuick.Controls as My

Rectangle {
    id: mainWindow

    height: parent.height
    width: parent.width

    signal showDevices

    property string frimVer: "..."
    property string mac: "..."
    property string status: "..."
    property string wifi_status: "..."
    property string wifi_signal: "..."
    property string smart_home_Status: "..."
    property int ota_progress: 0
    property string commandBytes: "bytes to be sent"
    property var status_array: [["0_0","0_1","0_2","0_3"],
                                ["1_0","1_1","1_2","1_3"],
                                ["2_0","2_1","2_2","2_3"],
                                ["3_0","3_1","3_2","3_3"],
                                ["4_0","4_1","4_2","4_3"],
                                ["5_0","5_1","5_2","5_3"],
                                ["6_0","6_1","6_2","6_3"],
                                ["7_0","7_1","7_2","7_3"],
                                ["8_0","8_1","8_2","8_3"],
                                ["9_0","9_1","9_2","9_3"],
                                ["10_0","10_1","10_2","10_3"]
                                ]


    Connections {
        target: Device

        function onServiceScanCompleted()
        {
            Device.connectToService("0xf3")
        }



        function onGuid_data_changed(data_type, data)
        {
            switch(data_type) {
                case "command_bytes":
                    mainWindow.commandBytes = data
                    break
                case "connection_status":
                    if(data === "connected") {
                        mainWindow.status = "connected"
                    } else {
                        mainWindow.status = "disconnected"
                        mainWindow.wifi_status = "disconnected"
                        mainWindow.wifi_signal = ".."
                        mainWindow.smart_home_Status = "disconnected"
                    }
                    break
                case "firm_ver":
                    mainWindow.frimVer = data
                    break
                case "wifi_status":
                    mainWindow.wifi_status = data
                    break
                case "wifi_level":
                    mainWindow.wifi_signal = data
                    break
                case "bsh_connection":
                    mainWindow.smart_home_Status = data
                    break
                case "ota_progress":
                    mainWindow.ota_progress = +data
                    break
                case "telemetry":
                    let j = 0
                    for (let i = 0; i < data.length; ){
                        // get actor substring
                        let actor = data.substring(i,i+3)
                        // get value length
                        let vle_len
                        for(vle_len = 0; vle_len < 9; vle_len++) {if(data[i+3+vle_len] === "|") {break}}

                        // some actors not to be shown
                        if(actor === "RST" || actor === "BCT" || actor === "DFV" || actor === "FUP" || actor === "FVR" || actor === "LOG")
                        {
                            i+=3 + vle_len + 1;
                            continue;
                        }

                        // set actor
                        let y_pos = j%11
                        let x_pos = Math.floor(j/11) > 0 ? Math.floor(j/11) + 1 : Math.floor(j/11)
                        mainWindow.status_array[y_pos][x_pos] = actor

                        // set value
                        i+=3
                        let vlue = data.substring(i,i+vle_len)
                        mainWindow.status_array[y_pos][x_pos+1] = vlue

                        i+=vle_len+1
                        j++
                    }
                    break
            }
            //TODO
            mainWindow.status_array = mainWindow.status_array  // this triggers ui update
        }

    }




    // Top row with device items
    Rectangle {
        id: top_row
        width: parent.width
        height: 72
        color: "#96d0b1"

        ColumnLayout {
//                id: layout
            anchors.fill: parent
            spacing: 1

            MainWindowHeaderItem {
                id: firmVer
                text_size: 14
                labelName: "Firmware ver:"
                labelValue: mainWindow.frimVer
                Layout.fillWidth: true
                Layout.leftMargin: 3
            }

            MainWindowHeaderItem {
                id: wifi_signal_text
                text_size: 14
                labelName: "WIFI signal: "
                labelValue: mainWindow.wifi_signal
                Layout.fillWidth: true
                Layout.leftMargin: 3
            }
        }
    }



    // controls buttons

    Rectangle {
        signal showDevices

        id: controls

        anchors.left: parent.left
        anchors.bottom: parent.bottom
        anchors.right: parent.right
        anchors.top: top_row.bottom

        color: "lightgrey"


        BleCmdButton {
            id: setWifiSbtn
            anchors.left: parent.left
            anchors.top: parent.top
            btnText: "Set Wifi ssid"
            btnValueHint: "enter ssid"
            text_size: 16
            onButtonClick: {
                Device.sendToBle("wifi_ssid", setWifiSbtn.btnData)
            }
        }

        BleCmdButton {
            id: setWifiPbtn
            anchors.left: parent.left
            anchors.top: setWifiSbtn.bottom
            text_size: 16
            btnText: "Set Wifi pswd"
            btnValueHint: "enter password"
            onButtonClick: {
                Device.sendToBle("wifi_pswd", setWifiPbtn.btnData)
            }
        }


        /***************************************s
        *     connect commands
        ****************************************/
        RowLayout {
            id: connectButtonsRect
            width: parent.width
//            height: 50
            anchors.top: setWifiPbtn.bottom
            anchors.topMargin: 3
            anchors.bottomMargin: 3
            spacing: 1

            JustButton {
                id: conWifiBtn
                btnText: "connect to WIFI"
                height: 50
                text_size: 18

                onButtonClick: {
                    Device.sendToBle("conn_wifi", "")
                }
            }

        }

        My.ComboBox {
            id: pickbox
            width: parent.width
            height: 60
            anchors.topMargin: 0
            font.pointSize: 16
            anchors.top: connectButtonsRect.bottom
            model: ["   Pick command",
                        "SWT    on/off",
                        "TTP    target T",
                        "MOD    mode",
                        "BRG    brightness",
                        "VOL    volume",
                        "LCK    lock",
                        "LED    LED on/off",
                        "OWD    open window",
                        "RST    reset",
                        "DLO    delayed off",
                        "SLO    sleep on/off",
                        "SLS    sleep start",
                        "SLE    sleep stop",
                        "SLW    sleep day",
                        "WMO    work on/off",
                        "WMS    work start",
                        "WME    work end",
                        "WMW    work day",
                        "BCT    read telemetry",
                        "OTA    firmware update",
                        ]


            onActivated: {
//                console.log(pickbox.currentText.substring(0,3))
                switch (pickbox.currentText.substring(0,3)) {
                    case "SWT":  value_pickbox.model = [0,1]; break
                    case "TTP":  value_pickbox.model = [5,7,10,20,28,30]; break
                    case "MOD":  value_pickbox.model = [0,1,2,3]; break
                    case "BRG":  value_pickbox.model = [0,1,2]; break
                    case "VOL":  value_pickbox.model = [0,1,2,3]; break
                    case "LCK":  value_pickbox.model = [0,1]; break
                    case "LED":  value_pickbox.model = [0,1]; break
                    case "OWD":  value_pickbox.model = [0,1]; break
                    case "RST":  value_pickbox.model = [1]; break
                    case "DLO":  value_pickbox.model = [1,10,20,60,100,200,720]; break
                    case "SLO":  value_pickbox.model = [0,1]; break
                    case "SLS":  value_pickbox.model = [1,10,20,60,100,200,1000]; break
                    case "SLE":  value_pickbox.model = [1,10,20,60,100,200,1000]; break
                    case "SLW":  value_pickbox.model = [1,3,7,15,8]; break
                    case "WMO":  value_pickbox.model = [0,10,11,12,13]; break
                    case "WMS":  value_pickbox.model = [1,10,20,60,100,200,1000]; break
                    case "WME":  value_pickbox.model = [1,10,20,60,100,200,1000]; break
                    case "WMW":  value_pickbox.model = [1,3,7,15,8]; break
                    case "BCT":  value_pickbox.model = ['-']; break
                    case "OTA":  value_pickbox.model = ['-']; break
                    case "   ":  value_pickbox.model = ['Pick command first']; break
                }
                Device.sendToO80x(pickbox.currentText, value_pickbox.currentText, true)
                }
        }

        Text {
            id: bytesText
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            anchors.top: pickbox.bottom
            text: commandBytes
            font.pointSize: 12
//            color: "grey"
        }


        My.ProgressBar {
            id: ota_progress_bar
            width: parent.width
            height: 30
            anchors.top: bytesText.bottom

            from: 0
            to: 100
            value: ota_progress
            visible: ota_progress > 0 ? true : false
            background: Rectangle {
                                            anchors.fill: ota_progress_bar
                                            anchors.leftMargin: 5
                                            anchors.rightMargin: 5
                                            color: "lightslategray"
//                                            radius: 4
//                                            border.width: 1
//                                            border.color: "black"
                                    }
            contentItem: Rectangle {
                                            anchors.left: ota_progress_bar.lefts
                                            anchors.bottom: ota_progress_bar.bottom

                                            anchors.leftMargin: 5
                                            anchors.rightMargin: 5

                                            height: ota_progress_bar.height
                                            width: ota_progress_bar.width * ota_progress_bar.value/100

                                            color: "lightgreen"
                                            radius: 4
                                        }
        }





        RowLayout {
            id: values_and_send
            width: parent.width
            height: 50
            anchors.top: ota_progress_bar.visible ? ota_progress_bar.bottom : bytesText.bottom
            anchors.topMargin: 2
            anchors.bottomMargin: 2
            spacing: 5


            My.ComboBox {
                id: value_pickbox
                Layout.fillWidth: true
                Layout.fillHeight: true
                anchors.topMargin: 0
                font.pointSize: 16
//                anchors.top: connectButtonsRect.bottom
                model: ["Pick value", "Pick command first"]
                onActivated: Device.sendToO80x(pickbox.currentText, value_pickbox.currentText, true);
            }

            JustButton {
                id: sendBtn
                btnText: "SEND"
                Layout.fillHeight: true
                Layout.fillWidth: true
                text_size: 16
                onButtonClick: {
                    Device.sendToO80x(pickbox.currentText, value_pickbox.currentText, false)
                }
            }

        }





        GridLayout {
               id: grid
                anchors.top: values_and_send.bottom
                width: parent.width
               rows: 11
               columns: 4
//            columnSpan: 2
//            rowSpan: 2

            Repeater {
                model: 11 * 4 // Total number of cells (rows * columns)
                Text {
                    required property int index
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.row: Math.floor(index / 4)
                    Layout.column: index % 4
                    text: status_array[Math.floor(index / 4)][index % 4]
                    font.pointSize: 16
                }
            }
        }





        // bottom buttons
        BottomButton {
            id: backBtn
            anchors.bottom: parent.bottom
//            anchors.left: connectBtn.right
            anchors.leftMargin: 5
            anchors.rightMargin: 5
            width: parent.width
            height: 90
            text_size: 18
            btnText: "Back to scanner"

            onButtonClick: {
                Device.disconnectFromDevice()
                showDevices()
                Device.update = "Search"
            }
        }

    }
}

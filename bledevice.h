#ifndef BLEDEVICE_H
#define BLEDEVICE_H

#include "characteristicinfo.h"
#include "serviceinfo.h"

#include <QtBluetooth/qlowenergycontroller.h>
#include <QtBluetooth/qlowenergyservice.h>

#include <QtCore/qobject.h>
#include <QtCore/qvariant.h>




#define CMD_SET_WIFI_SSID                   0x10
#define CMD_SET_WIFI_PSWD                   0x20
#define CMD_CONN_SH                         0x30
#define CMD_CONN_WIFI                       0x31
#define CMD_CONN_BLCR                       0x32
#define CMD_CONN_BROKER                     0x33
#define CMD_SET_BLCR_LINK                   0x40
#define CMD_MQTT_PSWD                       0x50
#define CMD_MQTT_UID                        0x60
#define CMD_SEND_NAME                       0x70
#define CMD_SEND_ID                         0x80
#define CMD_SEND_CHALLENGE                  0xA0
#define CMD_SEND_CHALLENGE_SIGN_SIZE        0xB0
#define CMD_SEND_CHALLENGE_SIGN_PIECE_SIZE  0xB1
#define CMD_SEND_CHALLENGE_SIGN_PIECE       0xB2

#define CMD_RESET                           0xF1 // should follow 0xF2 0xF3
#define CMD_SEND_TO_UART                    0xF5
#define CMD_FAKE_FROM_BROKER                0xF6
#define CMD_START_TEST                      0xF9
#define CMD_OTA_HARDCODED_LINK              0xFA
#define CMD_OTA                             0xFB
#define CMD_EMULATOR                        0x11 // its not implemented on device side...
#define CMD_DISABLE_OTA_CERT                0xFC
#define CMD_SEND_CERTIFICATE                0xC0

#define TODO_CMD_OFFSET                     0x100   
#define TODO_CMD_SEND_TO_BROKER             TODO_CMD_OFFSET + 0x01
#define TODO_CMD_GET_GUID                   TODO_CMD_OFFSET + 0x02
#define TODO_CMD_GET_BROKER_LINK            TODO_CMD_OFFSET + 0x03

#define ACTION_OFFSET                       0x200
#define ACTION_GET_CHALLENGE                0x01
#define ACTION_ENABLE_LOGS                  0x02

#define CMD_UNKNOWN                         0x3FF



class BleDevice : public QObject
{
    Q_OBJECT

public:
    BleDevice();
    virtual ~BleDevice();

    // Basic BLE operations
    void setControlService(ServiceInfo *service);
    ServiceInfo *getControlService();
    void setTxChar(const CharacteristicInfo *cr);
    void setRxChar(const CharacteristicInfo *cr);
    const CharacteristicInfo *getTxChar();

    // Common command method for all devices
    void sendCommandToDevice(QString command, QString data);
    void sendRawDataToDevice(QByteArray data);

    // Virtual methods to be implemented by device-specific classes
    virtual void handleCharacteristicChanged(const QLowEnergyCharacteristic &c, const QByteArray &value) = 0;
    virtual void handleCharacteristicRead(const QLowEnergyCharacteristic &info, const QByteArray &value) = 0;

    // Helper methods
    QString byteArrayToString(QByteArray arr);
    bool checkPointersNotNull();

    // Set callback functions
    static void setLogCallback(void (*callback)(const QString &message, const QString &color));
    static void setDataCallback(void (*callback)(const QString &data_type, const QString &data));

protected:
    ServiceInfo *m_service;
    const CharacteristicInfo *m_txChar;
    const CharacteristicInfo *m_rxChar;

    // Command-related data
    QString ssid;
    int next_ssid_piece_to_send;
    QString wifi_pswd;
    int next_pswd_piece_to_send;
    QString blcr_link;
    int next_blcr_piece_to_send;
    QString mqtt_uid;
    int next_uid_piece_to_send;
    QString mqtt_pswd;
    int next_mq_pswd_piece_to_send;

    // Callback functions for logging and data updates
    static void (*logCallback)(const QString &message, const QString &color);
    static void (*dataCallback)(const QString &data_type, const QString &data);

    // Log and data helper methods
    void log(const QString &message, const QString &color);
    void updateData(const QString &data_type, const QString &data);

    // Constants for logging
    const QString SYS_LOG_PREFIX = "- ";
    const QString SYS_LOG_COLOR = "lightgreen";
    const QString BLE_IN_LOG_PREFIX = "<< ";
    const QString BLE_IN_LOG_COLOR = "lightblue";
    const QString BLE_OUT_LOG_PREFIX = ">> ";
    const QString BLE_OUT_LOG_COLOR = "lightblue";
};

#endif // BLEDEVICE_H

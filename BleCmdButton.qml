
import QtQuick

Rectangle {
    id: bleCmdBtn

    property string btnText: "button"
    property string btnData: btnValue.text
    property string btnValueHint: "enter value"
    property int text_size: 12
    signal buttonClick

    width: parent.width - 10
    height: 35
    anchors.topMargin: 5
    anchors.rightMargin: 5
    anchors.leftMargin: 5

    Rectangle {
        id: btnRect
        width: 130
        height: bleCmdBtn.height
        anchors.left: parent.left
        anchors.verticalCenter: parent.verticalCenter
        color: "#96d0b1"
        Text {
            id: searchText
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            anchors.fill: parent
            text: bleCmdBtn.btnText
            elide: Text.ElideMiddle
            color: "black"
            font.pointSize: text_size
        }

        MouseArea {
            anchors.fill: parent
            onPressed: {
                btnRect.color = "#96b0b1"

            }

            onReleased: {
                btnRect.color = "#96d0b1"
            }

            onClicked: {
                btnValue.focus = false
                bleCmdBtn.buttonClick()
            }
        }

    }

    Rectangle {
        height: bleCmdBtn.height
        width: parent.width - btnRect.width - 25
        anchors.right: bleCmdBtn.right
//        anchors.leftMargin: 4
        anchors.verticalCenter: parent.verticalCenter
        anchors.rightMargin: 5

        color: "white"
//        border.color: "black"
//        border.width: 1
//        radius: 5

        TextEdit {
            id: btnValue
            text: btnValueHint

            anchors.fill: parent
            verticalAlignment: Text.AlignVCenter
            textMargin: 15
        }
    }
}

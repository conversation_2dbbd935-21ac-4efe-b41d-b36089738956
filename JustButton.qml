import QtQuick
import QtQuick.Layouts

Rectangle {
    id: justBtn

    property string btnText: "name"
    property int text_size: 10
    signal buttonClick

    height: 30
    implicitWidth: buttonText.implicitWidth
    Layout.fillWidth: true

    color: "#8ebfeb"

    Text {
        id: buttonText
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        anchors.fill: parent
        text: justBtn.btnText
        elide: Text.ElideMiddle
        color: "black"
        font.pointSize: text_size
    }

    MouseArea {
        anchors.fill: parent
        onPressed: {
            justBtn.color = "#8eafeb"
        }

        onReleased: {
            justBtn.color = "#8ebfeb"
        }

        onClicked: {
            justBtn.buttonClick()
        }
    }
}
